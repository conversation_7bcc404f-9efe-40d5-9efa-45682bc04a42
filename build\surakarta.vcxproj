﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{2D9ECA07-365E-3E6D-AFAA-63C4CC9DB468}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>surakarta</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">surakarta.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">surakarta</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">surakarta.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">surakarta</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">surakarta.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">surakarta</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">surakarta.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">surakarta</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalOptions>%(AdditionalOptions) /external:I "F:/libtorch/include" /external:I "F:/libtorch/include/torch/csrc/api/include" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4819;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_WARNINGS;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_WARNINGS;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\libtorch\include;F:\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\libtorch\include;F:\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>F:\libtorch\lib\c10.lib;F:\libtorch\lib\kineto.lib;F:\libtorch\lib\torch.lib;F:\libtorch\lib\torch_cpu.lib;F:\libtorch\lib\c10.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>/lib;/lib/$(Configuration);/lib/intel64;/lib/intel64/$(Configuration);/lib/intel64_win;/lib/intel64_win/$(Configuration);/lib/win-x64;/lib/win-x64/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/Debug/surakarta.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/Debug/surakarta.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalOptions>%(AdditionalOptions) /external:I "F:/libtorch/include" /external:I "F:/libtorch/include/torch/csrc/api/include" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4819;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\libtorch\include;F:\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\libtorch\include;F:\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>F:\libtorch\lib\c10.lib;F:\libtorch\lib\kineto.lib;F:\libtorch\lib\torch.lib;F:\libtorch\lib\torch_cpu.lib;F:\libtorch\lib\c10.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>/lib;/lib/$(Configuration);/lib/intel64;/lib/intel64/$(Configuration);/lib/intel64_win;/lib/intel64_win/$(Configuration);/lib/win-x64;/lib/win-x64/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/Release/surakarta.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/Release/surakarta.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalOptions>%(AdditionalOptions) /external:I "F:/libtorch/include" /external:I "F:/libtorch/include/torch/csrc/api/include" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4819;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\libtorch\include;F:\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\libtorch\include;F:\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>F:\libtorch\lib\c10.lib;F:\libtorch\lib\kineto.lib;F:\libtorch\lib\torch.lib;F:\libtorch\lib\torch_cpu.lib;F:\libtorch\lib\c10.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>/lib;/lib/$(Configuration);/lib/intel64;/lib/intel64/$(Configuration);/lib/intel64_win;/lib/intel64_win/$(Configuration);/lib/win-x64;/lib/win-x64/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/MinSizeRel/surakarta.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/MinSizeRel/surakarta.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalOptions>%(AdditionalOptions) /external:I "F:/libtorch/include" /external:I "F:/libtorch/include/torch/csrc/api/include" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4819;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\libtorch\include;F:\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\libtorch\include;F:\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>F:\libtorch\lib\c10.lib;F:\libtorch\lib\kineto.lib;F:\libtorch\lib\torch.lib;F:\libtorch\lib\torch_cpu.lib;F:\libtorch\lib\c10.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>/lib;/lib/$(Configuration);/lib/intel64;/lib/intel64/$(Configuration);/lib/intel64_win;/lib/intel64_win/$(Configuration);/lib/win-x64;/lib/win-x64/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/RelWithDebInfo/surakarta.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/RelWithDebInfo/surakarta.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
E:\ANACONDA\Lib\site-packages\cmake\data\bin\cmake.exe -SF:/竞赛/国赛/计算机博弈/Surakarta-AI-master -BF:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build --check-stamp-file F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompilerABI.c;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystem.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageHandleStandardArgs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageMessage.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckFlagCommonConfig.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Config.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets-release.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets.cmake;F:\libtorch\share\cmake\Caffe2\public\mkl.cmake;F:\libtorch\share\cmake\Caffe2\public\mkldnn.cmake;F:\libtorch\share\cmake\Caffe2\public\utils.cmake;F:\libtorch\share\cmake\Torch\TorchConfig.cmake;F:\libtorch\share\cmake\Torch\TorchConfigVersion.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCXXCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeRCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
E:\ANACONDA\Lib\site-packages\cmake\data\bin\cmake.exe -SF:/竞赛/国赛/计算机博弈/Surakarta-AI-master -BF:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build --check-stamp-file F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompilerABI.c;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystem.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageHandleStandardArgs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageMessage.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckFlagCommonConfig.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Config.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets-release.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets.cmake;F:\libtorch\share\cmake\Caffe2\public\mkl.cmake;F:\libtorch\share\cmake\Caffe2\public\mkldnn.cmake;F:\libtorch\share\cmake\Caffe2\public\utils.cmake;F:\libtorch\share\cmake\Torch\TorchConfig.cmake;F:\libtorch\share\cmake\Torch\TorchConfigVersion.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCXXCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeRCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
E:\ANACONDA\Lib\site-packages\cmake\data\bin\cmake.exe -SF:/竞赛/国赛/计算机博弈/Surakarta-AI-master -BF:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build --check-stamp-file F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompilerABI.c;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystem.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageHandleStandardArgs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageMessage.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckFlagCommonConfig.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Config.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets-release.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets.cmake;F:\libtorch\share\cmake\Caffe2\public\mkl.cmake;F:\libtorch\share\cmake\Caffe2\public\mkldnn.cmake;F:\libtorch\share\cmake\Caffe2\public\utils.cmake;F:\libtorch\share\cmake\Torch\TorchConfig.cmake;F:\libtorch\share\cmake\Torch\TorchConfigVersion.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCXXCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeRCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
E:\ANACONDA\Lib\site-packages\cmake\data\bin\cmake.exe -SF:/竞赛/国赛/计算机博弈/Surakarta-AI-master -BF:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build --check-stamp-file F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompilerABI.c;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystem.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageHandleStandardArgs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageMessage.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckFlagCommonConfig.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Config.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets-release.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets.cmake;F:\libtorch\share\cmake\Caffe2\public\mkl.cmake;F:\libtorch\share\cmake\Caffe2\public\mkldnn.cmake;F:\libtorch\share\cmake\Caffe2\public\utils.cmake;F:\libtorch\share\cmake\Torch\TorchConfig.cmake;F:\libtorch\share\cmake\Torch\TorchConfigVersion.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCXXCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeRCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\surakarta.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\ZERO_CHECK.vcxproj">
      <Project>{AE26BCF1-0B5E-3DAC-ABD2-83853D716600}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>