/**

The main program of Surakarta Game
------------------------------------------
This is the Project of Surakarta Game AI

Construced by <PERSON> and <PERSON>
Instructed by Professor <PERSON><PERSON><PERSON>dified to implement SAU Game Platform communication protocol
**/
#include <iterator>
#ifdef _WIN32
// Windows specific headers
#include <windows.h>
#else
// Unix/Linux specific headers
#include <unistd.h>
#endif
#include <typeinfo> 
#include <fstream>
#include <string.h>
#include <cstring>

#include "agent.h"
#include "episode.h"
#include "statistic.h"
#include "train.h"

// Function to clear input buffer (cross-platform)
void clearInputBuffer() {
#ifdef _WIN32
    // Windows specific method
    int c;
    while ((c = getchar()) != '\n' && c != EOF);
#else
    // Unix/Linux method
    fflush(stdin);
#endif
}

// Global variables for communication protocol
int Board[6][6]; // Board representation for protocol
PIECE computerSide; // AI side

// Convert internal board representation to protocol board representation
void convertBoardToProtocol(const board& b) {
    for (int i = 0; i < 6; i++) {
        for (int j = 0; j < 6; j++) {
            if (b(i, j) == BLACK) {
                Board[i][j] = 0; // BLACK
            } else if (b(i, j) == WHITE) {
                Board[i][j] = 1; // WHITE
            } else {
                Board[i][j] = 2; // EMPTY
            }
        }
    }
}

// Initialize board for protocol
void initProtocolBoard() {
    for (int i = 0; i < 2; ++i)
        for (int j = 0; j < 6; ++j)
            Board[i][j] = 0; // BLACK
    for (int i = 2; i < 4; ++i)
        for (int j = 0; j < 6; ++j)
            Board[i][j] = 2; // EMPTY
    for (int i = 4; i < 6; ++i)
        for (int j = 0; j < 6; ++j)
            Board[i][j] = 1; // WHITE
}

// Communication protocol implementation
int SuLaProtocol() {
    char message[256]; // Communication message buffer
    int start = 0; // Game start flag
    board b; // Internal board representation
    std::vector<board> prev_boards; // Previous boards for NN
    std::string mode = "eval"; // Default mode

    // Load the model if available
    if (std::ifstream("model.pt").good()) {
        torch::load(Net, "model.pt");
    }

    // Check if CUDA is available
    if (torch::cuda::is_available()) {
        device = torch::kCUDA;
    } else {
        device = torch::kCPU;
    }
    Net->to(device);

    while (1) {
        fflush(stdout);

        // Get platform message
        scanf("%s", message);

        // Analyze command
        if (strcmp(message, "move") == 0) {
            char moveStr[10];
            scanf("%s", moveStr);
            clearInputBuffer();

            // Process opponent's move
            if (strlen(moveStr) >= 4) {
                // In protocol: column-row format (AB means column A, row B)
                int startCol = moveStr[0] - 'A';
                int startRow = moveStr[1] - 'A';
                int endCol = moveStr[2] - 'A';
                int endRow = moveStr[3] - 'A';

                // Update internal board (row, col format)
                b(startRow, startCol) = SPACE;
                b(endRow, endCol) = (computerSide == BLACK) ? WHITE : BLACK;
                
                // Update protocol board
                Board[startRow][startCol] = 2; // EMPTY
                Board[endRow][endCol] = (computerSide == BLACK) ? 1 : 0; // Opponent's piece
                
                // Store previous board state
                prev_boards.push_back(b);
                if (prev_boards.size() > 3) prev_boards.erase(prev_boards.begin());
            }

            // Generate AI move
            Pair move;
            if (computerSide == BLACK) {
                move = Policy::NN(b, BLACK, prev_boards, mode);
            } else {
                move = Policy::NN(b, WHITE, prev_boards, mode);
            }

            // Update internal board with AI move
            b.move(move.prev, move.next, computerSide);
            
            // Update protocol board
            int startRow = move.prev / 6;
            int startCol = move.prev % 6;
            int endRow = move.next / 6;
            int endCol = move.next % 6;
            Board[startRow][startCol] = 2; // EMPTY
            Board[endRow][endCol] = (computerSide == BLACK) ? 0 : 1; // AI's piece

            // Store board state
            prev_boards.push_back(b);
            if (prev_boards.size() > 3) prev_boards.erase(prev_boards.begin());

            // Output move in protocol format (column, row)
            printf("move %c%c%c%c\n", 
                   startCol + 'A', startRow + 'A', 
                   endCol + 'A', endRow + 'A');
        }
        else if (strcmp(message, "new") == 0) {
            scanf("%s", message);
            clearInputBuffer();

            if (strcmp(message, "black") == 0) {
                computerSide = BLACK;
            } else {
                computerSide = WHITE;
            }

            // Initialize board
            b.init_board();
            initProtocolBoard();
            prev_boards.clear();
            prev_boards.push_back(b);
            start = 1;

            // If AI plays BLACK, make the first move
            if (computerSide == BLACK) {
                // Generate AI move
                Pair move = Policy::NN(b, BLACK, prev_boards, mode);

                // Update internal board
                b.move(move.prev, move.next, BLACK);
                
                // Update protocol board
                int startRow = move.prev / 6;
                int startCol = move.prev % 6;
                int endRow = move.next / 6;
                int endCol = move.next % 6;
                Board[startRow][startCol] = 2; // EMPTY
                Board[endRow][endCol] = (computerSide == BLACK) ? 0 : 1; // AI's piece

                // Store board state
                prev_boards.push_back(b);

                // Output move in protocol format (column, row)
                printf("move %c%c%c%c\n", 
                       startCol + 'A', startRow + 'A', 
                       endCol + 'A', endRow + 'A');
            }
        }
        else if (strcmp(message, "error") == 0) {
            clearInputBuffer();
            // Handle error - could retry with a different move
        }
        else if (strcmp(message, "name?") == 0) {
            clearInputBuffer();
            printf("name SurakartaAI\n");
        }
        else if (strcmp(message, "end") == 0) {
            clearInputBuffer();
            start = 0;
        }
        else if (strcmp(message, "quit") == 0) {
            clearInputBuffer();
            printf("Quit!\n");
            break;
        }
    }
    return 0;
}

void man_help() {
	std::cout << "Usege: ./surakarta [OPTION]...\n\n";
	std::cout << "A Surakarta Game Engine implementing different strategy, ex: Greedy, MCTS, AlphaGo(only value function), enjoy it!\n\n";
	
	std::cout << "[OPTION]...\n";
	std::cout << std::left << std::setw(30);
	std::cout << "  --total=NUM_OF_GAME" << "Set total numbers of games to run. Default value: 5\n\n";
	std::cout << std::left << std::setw(30);
	std::cout << "  --load=PATH_TO_FILE " << "Enter file name (path) to load model (Network parameters)\n" <<  std::setw(30) << " " << "for training and playing\n\n";
	std::cout << std::left << std::setw(30);
	std::cout << "  --save=PATH_TO_FILE " << "Enter file name (path) to save Network parameters\n" << std::setw(30) << " " << "after training.\n\n";
	std::cout << std::left << std::setw(30);
	std::cout << "  --mode=MODE" << "Two Modes: \"train\" for training,  \"eval\" for evaluation.\n" << std::setw(30) << " " << "Default value: \"train\"\n\n";
	std::cout << std::left << std::setw(30);
	std::cout << "  --black=POLICY" << "Three POLICYS: \"Greedy\", \"MCTS\", \"CNN\", \"Manual\" to choose.\n";
	std::cout << std::left << std::setw(30);
	std::cout << "  --white=POLICY" << "\n\n";
	std::cout << std::left << std::setw(30);
	std::cout << "  --protocol" << "Run in protocol mode for SAU Game Platform.\n\n";
}

int main(int argc, char* argv[]) {
	std::cout << "Surakarta Demo: ";
	std::copy(argv, argv + argc, std::ostream_iterator<char *>(std::cout, " "));
	std::cout << "\n\n";

	size_t total = 5, block = 0;
	std::string load_module;
	std::string save_module;
	std::string mode = "train";
	std::string black_policy = "CNN";
	std::string white_policy = "Greedy";
	bool protocol_mode = false;
	const int train_epoch = 1;  
	const int save_epoch = 250;
	
	for (int i{1}; i < argc; i++) {
		std::string para(argv[i]);
		if (para.find("--help") == 0) {
			man_help();
			return 0;
		}
		else if (para.find("--total=") == 0) {
			total = std::stoull(para.substr(para.find("=") + 1));
		}
		else if (para.find("--block=") == 0) {
			block = std::stoull(para.substr(para.find("=") + 1));
		}
		else if (para.find("--load=") == 0) {
			load_module = para.substr(para.find("=") + 1);
		}
		else if (para.find("--save=") == 0) {
			save_module = para.substr(para.find("=") + 1);
		}
		else if (para.find("--mode=") == 0) {
			mode = para.substr(para.find("=") + 1);
		}
		else if (para.find("--black=") == 0) {
			black_policy = para.substr(para.find("=") + 1);
		}
		else if (para.find("--white=") == 0) {
			white_policy = para.substr(para.find("=") + 1);
		}
		else if (para.find("--protocol") == 0) {
			protocol_mode = true;
		}
	}
	
	// Run in protocol mode if requested
	if (protocol_mode) {
		if (!load_module.empty()) {
			torch::load(Net, load_module);
		}
		return SuLaProtocol();
	}
	
	if (!load_module.empty()){
		torch::load(Net, load_module);
	}
	
	if (torch::cuda::is_available()) {
		std::cout << "Train on GPU\n";
		device = torch::kCUDA;
	}
	else {
		device = torch::kCPU;
		std::cout << "Train on CPU\n";
	}
	Net->to(device);


	statistic stat(total, block);

	player play {BLACK, black_policy};  // 0
	envir env {WHITE, white_policy};  // 1
	int cnt = 0;
	episode train_set_game;
	int save_cnt = 0;

	while (!stat.is_finished()) {

		board b;

		stat.open_episode("W:B");
		episode& game = stat.back(); 
		std::cout << b << '\n';
		while ( true ) {
			// player first (left)
			agent& who = game.take_turns(play, env);
			
			Pair mv = who.take_action( b, game.ep_boards, mode );
			
			// Print for Debug 
			std::cout << who.get_piece() << "'s turn.\t";
	 		std::cout << "Move from (" << mv.prev / 6 << ", " << mv.prev % 6 << ") to (" 
		 	<< mv.next / 6 << ", " << mv.next % 6 << ")\n";
			
			// end game
			if (mv == Pair{} || game.step() > episode::game_threshold)
				break;
			game.record_action(mv, b, who.get_piece());
			train_set_game.record_train_board(b, who.get_piece());
			
			std::cout << b << '\n';
		}

		// check draw game
		if ( game.check_draw(b) ) {
			train_set_game.train_close_episode(new agent());
			stat.close_episode("end", new agent(), b);
		}	
		else {
			agent &win = game.get_winner(env, play, b);
			// winner agent
			std::cout << win.name() << " Wins\n\n";	
			train_set_game.train_close_episode( &win );
			stat.close_episode("end", &win, b);
		}
		// train Network 
		if ( (++cnt) % train_epoch  == 0 && mode=="train" ) {
			std::cout << "Epoch : " << cnt << '\n';
			train_Net(train_set_game); // episode, epochs
			train_set_game.clear();
		}

		// save Network
		if ( (cnt) % save_epoch == 0 && !save_module.empty()) {
			std::cout << "Checkpoint in epoch " << cnt << '\n';
			save_cnt++;
			std::string module_name = save_module + std::to_string(save_cnt*save_epoch) + ".pt";
			torch::save(Net, module_name);
		}
	}
	return 0;
}
