cmake_minimum_required(VERSION 3.0 FATAL_ERROR)

project(surakarta)

# 增加编译器兼容性设置
if(MSVC)
  # 使用 C++14 而不是 C++17
  set(CMAKE_CXX_STANDARD 14)
  set(CMAKE_CXX_STANDARD_REQUIRED ON)
  # 禁用一些警告
  add_compile_options(/wd4819 /wd4996)
  # 预处理器定义
  add_definitions(-D_CRT_SECURE_NO_WARNINGS)
endif()

# Set libtorch path - use forward slashes for cross-platform compatibility
set(CMAKE_PREFIX_PATH "F:/libtorch")
find_package(Torch REQUIRED)

# Add include directories
include_directories(${TORCH_INCLUDE_DIRS})

# Add executable
add_executable(surakarta surakarta.cpp)

# Link libraries
target_link_libraries(surakarta "${TORCH_LIBRARIES}")

# Set compiler flags based on platform
if(MSVC)
  # MSVC specific flags
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3 /MP /EHsc")
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2")
else()
  # GCC/Clang flags
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Ofast")
endif()

# Print message about torch libraries
message(STATUS "TORCH_LIBRARIES: ${TORCH_LIBRARIES}")
