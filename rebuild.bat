@echo off
echo Surakarta AI - Clean and Rebuild Script
echo =====================================

REM 清理构建目录
echo 清理构建目录...
if exist build (
    rmdir /s /q build
    echo 已删除 build 目录
)

if exist build_mingw (
    rmdir /s /q build_mingw
    echo 已删除 build_mingw 目录
)

REM 询问是否下载新的 LibTorch
echo 是否需要下载新的 LibTorch 版本？
set /p DOWNLOAD_LIBTORCH="请输入 (Y/N): "
if /i "%DOWNLOAD_LIBTORCH%"=="Y" (
    call download_libtorch.bat
)

REM 创建构建目录
echo 创建新的构建目录...
mkdir build
cd build

REM 配置 CMake
echo 配置 CMake...
cmake -DCMAKE_BUILD_TYPE=Release ..

REM 构建项目
echo 构建项目...
cmake --build . --config Release

REM 检查结果
if %ERRORLEVEL% EQU 0 (
    echo.
    echo 构建成功!
    echo 可执行文件位于: %CD%\Release\surakarta.exe
) else (
    echo.
    echo 构建失败，错误代码: %ERRORLEVEL%
)

cd ..
pause 