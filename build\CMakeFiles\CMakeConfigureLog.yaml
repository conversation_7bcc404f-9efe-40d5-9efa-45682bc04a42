
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      适用于 .NET Framework MSBuild 版本 17.7.2+d6990bcfa
      生成启动时间为 2025/7/24 11:08:08。
      
      节点 1 上的项目“F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\3.29.5\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        正在创建目录“Debug\\CompilerIdC.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        正在对“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”执行 Touch 任务。
      ClCompile:
        E:\\visual studio 2022\\Professional\\VC\\Tools\\MSVC\\14.37.32822\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        E:\\visual studio 2022\\Professional\\VC\\Tools\\MSVC\\14.37.32822\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\3.29.5\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=E:\\visual studio 2022\\Professional\\VC\\Tools\\MSVC\\14.37.32822\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate”执行 Touch 任务。
      已完成生成项目“F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\3.29.5\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:03.88
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/3.29.5/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      适用于 .NET Framework MSBuild 版本 17.7.2+d6990bcfa
      生成启动时间为 2025/7/24 11:08:13。
      
      节点 1 上的项目“F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\3.29.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        正在创建目录“Debug\\CompilerIdCXX.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        正在对“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”执行 Touch 任务。
      ClCompile:
        E:\\visual studio 2022\\Professional\\VC\\Tools\\MSVC\\14.37.32822\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        E:\\visual studio 2022\\Professional\\VC\\Tools\\MSVC\\14.37.32822\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\3.29.5\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=E:\\visual studio 2022\\Professional\\VC\\Tools\\MSVC\\14.37.32822\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate”执行 Touch 任务。
      已完成生成项目“F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\3.29.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:03.63
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/3.29.5/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:67 (try_compile)"
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "F:/\u7ade\u8d5b/\u56fd\u8d5b/\u8ba1\u7b97\u673a\u535a\u5f08/Surakarta-AI-master/build/CMakeFiles/CMakeScratch/TryCompile-oyt70r"
      binary: "F:/\u7ade\u8d5b/\u56fd\u8d5b/\u8ba1\u7b97\u673a\u535a\u5f08/Surakarta-AI-master/build/CMakeFiles/CMakeScratch/TryCompile-oyt70r"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/CMakeScratch/TryCompile-oyt70r'
        
        Run Build Command(s): "E:/visual studio 2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_01181.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.7.2+d6990bcfa
        生成启动时间为 2025/7/24 11:08:18。
        
        节点 1 上的项目“F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oyt70r\\cmTC_01181.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_01181.dir\\Debug\\”。
          正在创建目录“F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oyt70r\\Debug\\”。
          正在创建目录“cmTC_01181.dir\\Debug\\cmTC_01181.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_01181.dir\\Debug\\cmTC_01181.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_01181.dir\\Debug\\cmTC_01181.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          E:\\visual studio 2022\\Professional\\VC\\Tools\\MSVC\\14.37.32822\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_01181.dir\\Debug\\\\" /Fd"cmTC_01181.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "E:\\ANACONDA\\Lib\\site-packages\\cmake\\data\\share\\cmake-3.29\\Modules\\CMakeCCompilerABI.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.37.32824 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_01181.dir\\Debug\\\\" /Fd"cmTC_01181.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "E:\\ANACONDA\\Lib\\site-packages\\cmake\\data\\share\\cmake-3.29\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          E:\\visual studio 2022\\Professional\\VC\\Tools\\MSVC\\14.37.32822\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oyt70r\\Debug\\cmTC_01181.exe" /INCREMENTAL /ILK:"cmTC_01181.dir\\Debug\\cmTC_01181.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/CMakeScratch/TryCompile-oyt70r/Debug/cmTC_01181.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/CMakeScratch/TryCompile-oyt70r/Debug/cmTC_01181.lib" /MACHINE:X64  /machine:x64 cmTC_01181.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_01181.vcxproj -> F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oyt70r\\Debug\\cmTC_01181.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_01181.dir\\Debug\\cmTC_01181.tlog\\unsuccessfulbuild”。
          正在对“cmTC_01181.dir\\Debug\\cmTC_01181.tlog\\cmTC_01181.lastbuildstate”执行 Touch 任务。
        已完成生成项目“F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oyt70r\\cmTC_01181.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:02.87
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:173 (message)"
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': E:/visual studio 2022/Professional/VC/Tools/MSVC/14.37.32822/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:210 (cmake_determine_linker_id)"
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the C compiler's linker: "E:/visual studio 2022/Professional/VC/Tools/MSVC/14.37.32822/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.37.32824.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:67 (try_compile)"
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "F:/\u7ade\u8d5b/\u56fd\u8d5b/\u8ba1\u7b97\u673a\u535a\u5f08/Surakarta-AI-master/build/CMakeFiles/CMakeScratch/TryCompile-10yswt"
      binary: "F:/\u7ade\u8d5b/\u56fd\u8d5b/\u8ba1\u7b97\u673a\u535a\u5f08/Surakarta-AI-master/build/CMakeFiles/CMakeScratch/TryCompile-10yswt"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/CMakeScratch/TryCompile-10yswt'
        
        Run Build Command(s): "E:/visual studio 2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_ae63a.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.7.2+d6990bcfa
        生成启动时间为 2025/7/24 11:08:22。
        
        节点 1 上的项目“F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-10yswt\\cmTC_ae63a.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_ae63a.dir\\Debug\\”。
          正在创建目录“F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-10yswt\\Debug\\”。
          正在创建目录“cmTC_ae63a.dir\\Debug\\cmTC_ae63a.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_ae63a.dir\\Debug\\cmTC_ae63a.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_ae63a.dir\\Debug\\cmTC_ae63a.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          E:\\visual studio 2022\\Professional\\VC\\Tools\\MSVC\\14.37.32822\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_ae63a.dir\\Debug\\\\" /Fd"cmTC_ae63a.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "E:\\ANACONDA\\Lib\\site-packages\\cmake\\data\\share\\cmake-3.29\\Modules\\CMakeCXXCompilerABI.cpp"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.37.32824 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_ae63a.dir\\Debug\\\\" /Fd"cmTC_ae63a.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "E:\\ANACONDA\\Lib\\site-packages\\cmake\\data\\share\\cmake-3.29\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          E:\\visual studio 2022\\Professional\\VC\\Tools\\MSVC\\14.37.32822\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-10yswt\\Debug\\cmTC_ae63a.exe" /INCREMENTAL /ILK:"cmTC_ae63a.dir\\Debug\\cmTC_ae63a.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/CMakeScratch/TryCompile-10yswt/Debug/cmTC_ae63a.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/CMakeScratch/TryCompile-10yswt/Debug/cmTC_ae63a.lib" /MACHINE:X64  /machine:x64 cmTC_ae63a.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_ae63a.vcxproj -> F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-10yswt\\Debug\\cmTC_ae63a.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_ae63a.dir\\Debug\\cmTC_ae63a.tlog\\unsuccessfulbuild”。
          正在对“cmTC_ae63a.dir\\Debug\\cmTC_ae63a.tlog\\cmTC_ae63a.lastbuildstate”执行 Touch 任务。
        已完成生成项目“F:\\竞赛\\国赛\\计算机博弈\\Surakarta-AI-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-10yswt\\cmTC_ae63a.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:02.83
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:173 (message)"
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': E:/visual studio 2022/Professional/VC/Tools/MSVC/14.37.32822/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:210 (cmake_determine_linker_id)"
      - "E:/ANACONDA/Lib/site-packages/cmake/data/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "E:/visual studio 2022/Professional/VC/Tools/MSVC/14.37.32822/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.37.32824.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
