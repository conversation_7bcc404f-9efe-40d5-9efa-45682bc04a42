﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{F907F383-0F3B-31C7-A83A-C826DE911197}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\libtorch\include;F:\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\libtorch\include;F:\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\libtorch\include;F:\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\libtorch\include;F:\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
E:\ANACONDA\Lib\site-packages\cmake\data\bin\cmake.exe -SF:/竞赛/国赛/计算机博弈/Surakarta-AI-master -BF:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build --check-stamp-file F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompilerABI.c;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystem.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageHandleStandardArgs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageMessage.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckFlagCommonConfig.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Config.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets-release.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets.cmake;F:\libtorch\share\cmake\Caffe2\public\mkl.cmake;F:\libtorch\share\cmake\Caffe2\public\mkldnn.cmake;F:\libtorch\share\cmake\Caffe2\public\utils.cmake;F:\libtorch\share\cmake\Torch\TorchConfig.cmake;F:\libtorch\share\cmake\Torch\TorchConfigVersion.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCXXCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeRCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
E:\ANACONDA\Lib\site-packages\cmake\data\bin\cmake.exe -SF:/竞赛/国赛/计算机博弈/Surakarta-AI-master -BF:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build --check-stamp-file F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompilerABI.c;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystem.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageHandleStandardArgs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageMessage.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckFlagCommonConfig.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Config.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets-release.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets.cmake;F:\libtorch\share\cmake\Caffe2\public\mkl.cmake;F:\libtorch\share\cmake\Caffe2\public\mkldnn.cmake;F:\libtorch\share\cmake\Caffe2\public\utils.cmake;F:\libtorch\share\cmake\Torch\TorchConfig.cmake;F:\libtorch\share\cmake\Torch\TorchConfigVersion.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCXXCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeRCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
E:\ANACONDA\Lib\site-packages\cmake\data\bin\cmake.exe -SF:/竞赛/国赛/计算机博弈/Surakarta-AI-master -BF:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build --check-stamp-file F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompilerABI.c;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystem.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageHandleStandardArgs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageMessage.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckFlagCommonConfig.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Config.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets-release.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets.cmake;F:\libtorch\share\cmake\Caffe2\public\mkl.cmake;F:\libtorch\share\cmake\Caffe2\public\mkldnn.cmake;F:\libtorch\share\cmake\Caffe2\public\utils.cmake;F:\libtorch\share\cmake\Torch\TorchConfig.cmake;F:\libtorch\share\cmake\Torch\TorchConfigVersion.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCXXCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeRCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
E:\ANACONDA\Lib\site-packages\cmake\data\bin\cmake.exe -SF:/竞赛/国赛/计算机博弈/Surakarta-AI-master -BF:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build --check-stamp-file F:/竞赛/国赛/计算机博弈/Surakarta-AI-master/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompilerABI.c;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystem.cmake.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CheckCXXSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageHandleStandardArgs.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\FindPackageMessage.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckCompilerFlag.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckFlagCommonConfig.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CheckSourceCompiles.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows.cmake;E:\ANACONDA\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Config.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets-release.cmake;F:\libtorch\share\cmake\Caffe2\Caffe2Targets.cmake;F:\libtorch\share\cmake\Caffe2\public\mkl.cmake;F:\libtorch\share\cmake\Caffe2\public\mkldnn.cmake;F:\libtorch\share\cmake\Caffe2\public\utils.cmake;F:\libtorch\share\cmake\Torch\TorchConfig.cmake;F:\libtorch\share\cmake\Torch\TorchConfigVersion.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeCXXCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeRCCompiler.cmake;F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\3.29.5\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\ZERO_CHECK.vcxproj">
      <Project>{AE26BCF1-0B5E-3DAC-ABD2-83853D716600}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\竞赛\国赛\计算机博弈\Surakarta-AI-master\build\surakarta.vcxproj">
      <Project>{2D9ECA07-365E-3E6D-AFAA-63C4CC9DB468}</Project>
      <Name>surakarta</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>