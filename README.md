# Surakarta AI

This is an AI engine for the Surakarta board game, modified to work with the SAU Game Platform communication protocol.

## Game Description

Surakarta is a board game played on a 6×6 grid with 12 pieces per player. The game is named after the city of Surakarta in Java, Indonesia.

## Features

- Implements various AI strategies:
  - Neural Network-based AI (CNN)
  - Monte Carlo Tree Search (MCTS)
  - Greedy algorithm
  - Manual play mode
- Compatible with SAU Game Platform communication protocol
- Training and evaluation modes

## Building the Project

### Prerequisites

- CMake (3.0 or higher)
- C++17 compatible compiler (Visual Studio, MinGW, GCC, etc.)
- LibTorch (PyTorch C++ API)

### Windows Build Instructions

#### 使用批处理文件构建 (推荐)

### Linux/macOS Build Instructions

## Usage

### Training Mode

To train the AI:

```bash
./surakarta --mode=train --black=CNN --white=Greedy --save=model
```

### Evaluation Mode

To evaluate the AI:

```bash
./surakarta --mode=eval --black=CNN --white=MCTS --load=model.pt
```

### Protocol Mode (for SAU Game Platform)

To run in protocol mode for compatibility with SAU Game Platform:

```bash
./surakarta --protocol --load=model.pt
```

### Command Line Options

- `--total=NUM`: Set total number of games to run (default: 5)
- `--load=PATH`: Path to load model file
- `--save=PATH`: Path to save model file
- `--mode=MODE`: "train" or "eval" (default: "train")
- `--black=POLICY`: Policy for black player ("Greedy", "MCTS", "CNN", "Manual")
- `--white=POLICY`: Policy for white player
- `--protocol`: Run in protocol mode for SAU Game Platform

## Communication Protocol

The engine implements the SAU Game Platform communication protocol as specified. In protocol mode, it responds to the following commands:

- `name?`: Returns the engine name
- `new [color]`: Creates a new game with the engine playing as the specified color
- `move [coordinates]`: Processes opponent's move and responds with the engine's move
- `end`: Ends the current game
- `quit`: Exits the engine

## License

This project is based on work by Clive Wu and Mark Chang, instructed by Professor I-Chen Wu.
